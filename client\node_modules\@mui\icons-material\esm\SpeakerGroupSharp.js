"use client";

import createSvgIcon from './utils/createSvgIcon';
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "M20 1H8v17.99h12zm-6 2c1.1 0 2 .89 2 2s-.9 2-2 2-2-.89-2-2 .9-2 2-2m0 13.5c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4"
}, "0"), /*#__PURE__*/_jsx("circle", {
  cx: "14",
  cy: "12.5",
  r: "2.5"
}, "1"), /*#__PURE__*/_jsx("path", {
  d: "M6 5H4v18h12v-2H6z"
}, "2")], 'SpeakerGroupSharp');