import { UseSnackbarParameters, UseSnackbarReturnValue } from './useSnackbar.types';
/**
 * The basic building block for creating custom snackbar.
 *
 * Demos:
 *
 * - [Snackbar](https://mui.com/base-ui/react-snackbar/#hook)
 *
 * API:
 *
 * - [useSnackbar API](https://mui.com/base-ui/react-snackbar/hooks-api/#use-snackbar)
 */
declare function useSnackbar(parameters?: UseSnackbarParameters): UseSnackbarReturnValue;
export default useSnackbar;
