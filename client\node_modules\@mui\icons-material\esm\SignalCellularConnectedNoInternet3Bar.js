import * as React from 'react';
import createSvgIcon from './utils/createSvgIcon';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
export default createSvgIcon( /*#__PURE__*/_jsxs(React.Fragment, {
  children: [/*#__PURE__*/_jsx("path", {
    fillOpacity: ".3",
    d: "M22 8V2L2 22h16V8z"
  }), /*#__PURE__*/_jsx("path", {
    d: "M17 22V7L2 22h15zm3-12v8h2v-8h-2zm0 12h2v-2h-2v2z"
  })]
}), 'SignalCellularConnectedNoInternet3Bar');