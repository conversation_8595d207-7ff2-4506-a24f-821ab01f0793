const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Passage = sequelize.define('Passage', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  id_badge: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'badge',
      key: 'id'
    }
  },
  id_porte: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'porte',
      key: 'id'
    }
  },
  date_acces: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  type_acces: {
    type: DataTypes.STRING(10),
    allowNull: false,
    validate: {
      isIn: [['entree', 'sortie']]
    }
  },
  resultat: {
    type: DataTypes.STRING(20),
    defaultValue: 'autorise',
    validate: {
      isIn: [['autorise', 'refuse', 'erreur']]
    }
  }
}, {
  tableName: 'passage',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false // Pas de updatedAt pour les passages
});

module.exports = Passage;
