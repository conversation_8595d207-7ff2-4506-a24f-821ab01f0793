"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _index = _interopRequireDefault(require("../en-US/_lib/formatDistance/index.js"));
var _index2 = _interopRequireDefault(require("../en-US/_lib/formatRelative/index.js"));
var _index3 = _interopRequireDefault(require("../en-US/_lib/localize/index.js"));
var _index4 = _interopRequireDefault(require("../en-US/_lib/match/index.js"));
var _index5 = _interopRequireDefault(require("./_lib/formatLong/index.js"));
/**
 * @type {Locale}
 * @category Locales
 * @summary English locale (South Africa).
 * @language English
 * @iso-639-2 eng
 * <AUTHOR> [@shaykav]{@link https://github.com/shaykav}
 */
var locale = {
  code: 'en-ZA',
  formatDistance: _index.default,
  formatLong: _index5.default,
  formatRelative: _index2.default,
  localize: _index3.default,
  match: _index4.default,
  options: {
    weekStartsOn: 0,
    // Sunday is the first day of the week.
    firstWeekContainsDate: 1 // The week that contains Jan 1st is the first week of the year.
  }
};
var _default = locale;
exports.default = _default;
module.exports = exports.default;