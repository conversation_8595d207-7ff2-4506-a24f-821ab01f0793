'use client';

import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _extends from "@babel/runtime/helpers/esm/extends";
import _toConsumableArray from "@babel/runtime/helpers/esm/toConsumableArray";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import * as React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import chainPropTypes from '@mui/utils/chainPropTypes';
import composeClasses from '@mui/utils/composeClasses';
import { alpha, lighten, darken } from '@mui/system/colorManipulator';
import { useRtl } from '@mui/system/RtlProvider';
import useSlotProps from '@mui/utils/useSlotProps';
import isHostComponent from '@mui/utils/isHostComponent';
import { useSlider, valueToPercent } from './useSlider';
import { styled } from '../zero-styled';
import { useDefaultProps } from '../DefaultPropsProvider';
import slotShouldForwardProp from '../styles/slotShouldForwardProp';
import shouldSpreadAdditionalProps from '../utils/shouldSpreadAdditionalProps';
import capitalize from '../utils/capitalize';
import BaseSliderValueLabel from './SliderValueLabel';
import sliderClasses, { getSliderUtilityClass } from './sliderClasses';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
function Identity(x) {
  return x;
}
export var SliderRoot = styled('span', {
  name: 'MuiSlider',
  slot: 'Root',
  overridesResolver: function overridesResolver(props, styles) {
    var ownerState = props.ownerState;
    return [styles.root, styles["color".concat(capitalize(ownerState.color))], ownerState.size !== 'medium' && styles["size".concat(capitalize(ownerState.size))], ownerState.marked && styles.marked, ownerState.orientation === 'vertical' && styles.vertical, ownerState.track === 'inverted' && styles.trackInverted, ownerState.track === false && styles.trackFalse];
  }
})(function (_ref) {
  var _theme$vars;
  var theme = _ref.theme;
  return _defineProperty(_defineProperty(_defineProperty({
    borderRadius: 12,
    boxSizing: 'content-box',
    display: 'inline-block',
    position: 'relative',
    cursor: 'pointer',
    touchAction: 'none',
    WebkitTapHighlightColor: 'transparent',
    '@media print': {
      colorAdjust: 'exact'
    }
  }, "&.".concat(sliderClasses.disabled), {
    pointerEvents: 'none',
    cursor: 'default',
    color: (theme.vars || theme).palette.grey[400]
  }), "&.".concat(sliderClasses.dragging), _defineProperty({}, "& .".concat(sliderClasses.thumb, ", & .").concat(sliderClasses.track), {
    transition: 'none'
  })), "variants", [].concat(_toConsumableArray(Object.keys(((_theme$vars = theme.vars) != null ? _theme$vars : theme).palette).filter(function (key) {
    var _theme$vars2;
    return ((_theme$vars2 = theme.vars) != null ? _theme$vars2 : theme).palette[key].main;
  }).map(function (color) {
    return {
      props: {
        color: color
      },
      style: {
        color: (theme.vars || theme).palette[color].main
      }
    };
  })), [{
    props: {
      orientation: 'horizontal'
    },
    style: {
      height: 4,
      width: '100%',
      padding: '13px 0',
      // The primary input mechanism of the device includes a pointing device of limited accuracy.
      '@media (pointer: coarse)': {
        // Reach 42px touch target, about ~8mm on screen.
        padding: '20px 0'
      }
    }
  }, {
    props: {
      orientation: 'horizontal',
      size: 'small'
    },
    style: {
      height: 2
    }
  }, {
    props: {
      orientation: 'horizontal',
      marked: true
    },
    style: {
      marginBottom: 20
    }
  }, {
    props: {
      orientation: 'vertical'
    },
    style: {
      height: '100%',
      width: 4,
      padding: '0 13px',
      // The primary input mechanism of the device includes a pointing device of limited accuracy.
      '@media (pointer: coarse)': {
        // Reach 42px touch target, about ~8mm on screen.
        padding: '0 20px'
      }
    }
  }, {
    props: {
      orientation: 'vertical',
      size: 'small'
    },
    style: {
      width: 2
    }
  }, {
    props: {
      orientation: 'vertical',
      marked: true
    },
    style: {
      marginRight: 44
    }
  }]));
});
export var SliderRail = styled('span', {
  name: 'MuiSlider',
  slot: 'Rail',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.rail;
  }
})({
  display: 'block',
  position: 'absolute',
  borderRadius: 'inherit',
  backgroundColor: 'currentColor',
  opacity: 0.38,
  variants: [{
    props: {
      orientation: 'horizontal'
    },
    style: {
      width: '100%',
      height: 'inherit',
      top: '50%',
      transform: 'translateY(-50%)'
    }
  }, {
    props: {
      orientation: 'vertical'
    },
    style: {
      height: '100%',
      width: 'inherit',
      left: '50%',
      transform: 'translateX(-50%)'
    }
  }, {
    props: {
      track: 'inverted'
    },
    style: {
      opacity: 1
    }
  }]
});
export var SliderTrack = styled('span', {
  name: 'MuiSlider',
  slot: 'Track',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.track;
  }
})(function (_ref3) {
  var _theme$vars3;
  var theme = _ref3.theme;
  return {
    display: 'block',
    position: 'absolute',
    borderRadius: 'inherit',
    border: '1px solid currentColor',
    backgroundColor: 'currentColor',
    transition: theme.transitions.create(['left', 'width', 'bottom', 'height'], {
      duration: theme.transitions.duration.shortest
    }),
    variants: [{
      props: {
        size: 'small'
      },
      style: {
        border: 'none'
      }
    }, {
      props: {
        orientation: 'horizontal'
      },
      style: {
        height: 'inherit',
        top: '50%',
        transform: 'translateY(-50%)'
      }
    }, {
      props: {
        orientation: 'vertical'
      },
      style: {
        width: 'inherit',
        left: '50%',
        transform: 'translateX(-50%)'
      }
    }, {
      props: {
        track: false
      },
      style: {
        display: 'none'
      }
    }].concat(_toConsumableArray(Object.keys(((_theme$vars3 = theme.vars) != null ? _theme$vars3 : theme).palette).filter(function (key) {
      var _theme$vars4;
      return ((_theme$vars4 = theme.vars) != null ? _theme$vars4 : theme).palette[key].main;
    }).map(function (color) {
      return {
        props: {
          color: color,
          track: 'inverted'
        },
        style: _extends({}, theme.vars ? {
          backgroundColor: theme.vars.palette.Slider["".concat(color, "Track")],
          borderColor: theme.vars.palette.Slider["".concat(color, "Track")]
        } : _extends({
          backgroundColor: lighten(theme.palette[color].main, 0.62),
          borderColor: lighten(theme.palette[color].main, 0.62)
        }, theme.applyStyles('dark', {
          backgroundColor: darken(theme.palette[color].main, 0.5)
        }), theme.applyStyles('dark', {
          borderColor: darken(theme.palette[color].main, 0.5)
        })))
      };
    })))
  };
});
export var SliderThumb = styled('span', {
  name: 'MuiSlider',
  slot: 'Thumb',
  overridesResolver: function overridesResolver(props, styles) {
    var ownerState = props.ownerState;
    return [styles.thumb, styles["thumbColor".concat(capitalize(ownerState.color))], ownerState.size !== 'medium' && styles["thumbSize".concat(capitalize(ownerState.size))]];
  }
})(function (_ref4) {
  var _theme$vars5;
  var theme = _ref4.theme;
  return _defineProperty(_defineProperty({
    position: 'absolute',
    width: 20,
    height: 20,
    boxSizing: 'border-box',
    borderRadius: '50%',
    outline: 0,
    backgroundColor: 'currentColor',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: theme.transitions.create(['box-shadow', 'left', 'bottom'], {
      duration: theme.transitions.duration.shortest
    }),
    '&::before': {
      position: 'absolute',
      content: '""',
      borderRadius: 'inherit',
      width: '100%',
      height: '100%',
      boxShadow: (theme.vars || theme).shadows[2]
    },
    '&::after': {
      position: 'absolute',
      content: '""',
      borderRadius: '50%',
      // 42px is the hit target
      width: 42,
      height: 42,
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    }
  }, "&.".concat(sliderClasses.disabled), {
    '&:hover': {
      boxShadow: 'none'
    }
  }), "variants", [{
    props: {
      size: 'small'
    },
    style: {
      width: 12,
      height: 12,
      '&::before': {
        boxShadow: 'none'
      }
    }
  }, {
    props: {
      orientation: 'horizontal'
    },
    style: {
      top: '50%',
      transform: 'translate(-50%, -50%)'
    }
  }, {
    props: {
      orientation: 'vertical'
    },
    style: {
      left: '50%',
      transform: 'translate(-50%, 50%)'
    }
  }].concat(_toConsumableArray(Object.keys(((_theme$vars5 = theme.vars) != null ? _theme$vars5 : theme).palette).filter(function (key) {
    var _theme$vars6;
    return ((_theme$vars6 = theme.vars) != null ? _theme$vars6 : theme).palette[key].main;
  }).map(function (color) {
    return {
      props: {
        color: color
      },
      style: _defineProperty(_defineProperty({}, "&:hover, &.".concat(sliderClasses.focusVisible), _extends({}, theme.vars ? {
        boxShadow: "0px 0px 0px 8px rgba(".concat(theme.vars.palette[color].mainChannel, " / 0.16)")
      } : {
        boxShadow: "0px 0px 0px 8px ".concat(alpha(theme.palette[color].main, 0.16))
      }, {
        '@media (hover: none)': {
          boxShadow: 'none'
        }
      })), "&.".concat(sliderClasses.active), _extends({}, theme.vars ? {
        boxShadow: "0px 0px 0px 14px rgba(".concat(theme.vars.palette[color].mainChannel, " / 0.16)")
      } : {
        boxShadow: "0px 0px 0px 14px ".concat(alpha(theme.palette[color].main, 0.16))
      }))
    };
  }))));
});
export var SliderValueLabel = styled(BaseSliderValueLabel, {
  name: 'MuiSlider',
  slot: 'ValueLabel',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.valueLabel;
  }
})(function (_ref6) {
  var theme = _ref6.theme;
  return _extends({
    zIndex: 1,
    whiteSpace: 'nowrap'
  }, theme.typography.body2, {
    fontWeight: 500,
    transition: theme.transitions.create(['transform'], {
      duration: theme.transitions.duration.shortest
    }),
    position: 'absolute',
    backgroundColor: (theme.vars || theme).palette.grey[600],
    borderRadius: 2,
    color: (theme.vars || theme).palette.common.white,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '0.25rem 0.75rem',
    variants: [{
      props: {
        orientation: 'horizontal'
      },
      style: _defineProperty({
        transform: 'translateY(-100%) scale(0)',
        top: '-10px',
        transformOrigin: 'bottom center',
        '&::before': {
          position: 'absolute',
          content: '""',
          width: 8,
          height: 8,
          transform: 'translate(-50%, 50%) rotate(45deg)',
          backgroundColor: 'inherit',
          bottom: 0,
          left: '50%'
        }
      }, "&.".concat(sliderClasses.valueLabelOpen), {
        transform: 'translateY(-100%) scale(1)'
      })
    }, {
      props: {
        orientation: 'vertical'
      },
      style: _defineProperty({
        transform: 'translateY(-50%) scale(0)',
        right: '30px',
        top: '50%',
        transformOrigin: 'right center',
        '&::before': {
          position: 'absolute',
          content: '""',
          width: 8,
          height: 8,
          transform: 'translate(-50%, -50%) rotate(45deg)',
          backgroundColor: 'inherit',
          right: -8,
          top: '50%'
        }
      }, "&.".concat(sliderClasses.valueLabelOpen), {
        transform: 'translateY(-50%) scale(1)'
      })
    }, {
      props: {
        size: 'small'
      },
      style: {
        fontSize: theme.typography.pxToRem(12),
        padding: '0.25rem 0.5rem'
      }
    }, {
      props: {
        orientation: 'vertical',
        size: 'small'
      },
      style: {
        right: '20px'
      }
    }]
  });
});
export var SliderMark = styled('span', {
  name: 'MuiSlider',
  slot: 'Mark',
  shouldForwardProp: function shouldForwardProp(prop) {
    return slotShouldForwardProp(prop) && prop !== 'markActive';
  },
  overridesResolver: function overridesResolver(props, styles) {
    var markActive = props.markActive;
    return [styles.mark, markActive && styles.markActive];
  }
})(function (_ref7) {
  var theme = _ref7.theme;
  return {
    position: 'absolute',
    width: 2,
    height: 2,
    borderRadius: 1,
    backgroundColor: 'currentColor',
    variants: [{
      props: {
        orientation: 'horizontal'
      },
      style: {
        top: '50%',
        transform: 'translate(-1px, -50%)'
      }
    }, {
      props: {
        orientation: 'vertical'
      },
      style: {
        left: '50%',
        transform: 'translate(-50%, 1px)'
      }
    }, {
      props: {
        markActive: true
      },
      style: {
        backgroundColor: (theme.vars || theme).palette.background.paper,
        opacity: 0.8
      }
    }]
  };
});
export var SliderMarkLabel = styled('span', {
  name: 'MuiSlider',
  slot: 'MarkLabel',
  shouldForwardProp: function shouldForwardProp(prop) {
    return slotShouldForwardProp(prop) && prop !== 'markLabelActive';
  },
  overridesResolver: function overridesResolver(props, styles) {
    return styles.markLabel;
  }
})(function (_ref8) {
  var theme = _ref8.theme;
  return _extends({}, theme.typography.body2, {
    color: (theme.vars || theme).palette.text.secondary,
    position: 'absolute',
    whiteSpace: 'nowrap',
    variants: [{
      props: {
        orientation: 'horizontal'
      },
      style: {
        top: 30,
        transform: 'translateX(-50%)',
        '@media (pointer: coarse)': {
          top: 40
        }
      }
    }, {
      props: {
        orientation: 'vertical'
      },
      style: {
        left: 36,
        transform: 'translateY(50%)',
        '@media (pointer: coarse)': {
          left: 44
        }
      }
    }, {
      props: {
        markLabelActive: true
      },
      style: {
        color: (theme.vars || theme).palette.text.primary
      }
    }]
  });
});
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var disabled = ownerState.disabled,
    dragging = ownerState.dragging,
    marked = ownerState.marked,
    orientation = ownerState.orientation,
    track = ownerState.track,
    classes = ownerState.classes,
    color = ownerState.color,
    size = ownerState.size;
  var slots = {
    root: ['root', disabled && 'disabled', dragging && 'dragging', marked && 'marked', orientation === 'vertical' && 'vertical', track === 'inverted' && 'trackInverted', track === false && 'trackFalse', color && "color".concat(capitalize(color)), size && "size".concat(capitalize(size))],
    rail: ['rail'],
    track: ['track'],
    mark: ['mark'],
    markActive: ['markActive'],
    markLabel: ['markLabel'],
    markLabelActive: ['markLabelActive'],
    valueLabel: ['valueLabel'],
    thumb: ['thumb', disabled && 'disabled', size && "thumbSize".concat(capitalize(size)), color && "thumbColor".concat(capitalize(color))],
    active: ['active'],
    disabled: ['disabled'],
    focusVisible: ['focusVisible']
  };
  return composeClasses(slots, getSliderUtilityClass, classes);
};
var Forward = function Forward(_ref9) {
  var children = _ref9.children;
  return children;
};
var Slider = /*#__PURE__*/React.forwardRef(function Slider(inputProps, ref) {
  var _ref10, _slots$root, _ref11, _slots$rail, _ref12, _slots$track, _ref13, _slots$thumb, _ref14, _slots$valueLabel, _ref15, _slots$mark, _ref16, _slots$markLabel, _ref17, _slots$input, _slotProps$root, _slotProps$rail, _slotProps$track, _slotProps$thumb, _slotProps$valueLabel, _slotProps$mark, _slotProps$markLabel, _slotProps$input;
  var props = useDefaultProps({
    props: inputProps,
    name: 'MuiSlider'
  });
  var isRtl = useRtl();
  var ariaLabel = props['aria-label'],
    ariaValuetext = props['aria-valuetext'],
    ariaLabelledby = props['aria-labelledby'],
    _props$component = props.component,
    component = _props$component === void 0 ? 'span' : _props$component,
    _props$components = props.components,
    components = _props$components === void 0 ? {} : _props$components,
    _props$componentsProp = props.componentsProps,
    componentsProps = _props$componentsProp === void 0 ? {} : _props$componentsProp,
    _props$color = props.color,
    color = _props$color === void 0 ? 'primary' : _props$color,
    classesProp = props.classes,
    className = props.className,
    _props$disableSwap = props.disableSwap,
    disableSwap = _props$disableSwap === void 0 ? false : _props$disableSwap,
    _props$disabled = props.disabled,
    disabled = _props$disabled === void 0 ? false : _props$disabled,
    getAriaLabel = props.getAriaLabel,
    getAriaValueText = props.getAriaValueText,
    _props$marks = props.marks,
    marksProp = _props$marks === void 0 ? false : _props$marks,
    _props$max = props.max,
    max = _props$max === void 0 ? 100 : _props$max,
    _props$min = props.min,
    min = _props$min === void 0 ? 0 : _props$min,
    name = props.name,
    onChange = props.onChange,
    onChangeCommitted = props.onChangeCommitted,
    _props$orientation = props.orientation,
    orientation = _props$orientation === void 0 ? 'horizontal' : _props$orientation,
    _props$shiftStep = props.shiftStep,
    shiftStep = _props$shiftStep === void 0 ? 10 : _props$shiftStep,
    _props$size = props.size,
    size = _props$size === void 0 ? 'medium' : _props$size,
    _props$step = props.step,
    step = _props$step === void 0 ? 1 : _props$step,
    _props$scale = props.scale,
    scale = _props$scale === void 0 ? Identity : _props$scale,
    slotProps = props.slotProps,
    slots = props.slots,
    tabIndex = props.tabIndex,
    _props$track = props.track,
    track = _props$track === void 0 ? 'normal' : _props$track,
    valueProp = props.value,
    _props$valueLabelDisp = props.valueLabelDisplay,
    valueLabelDisplay = _props$valueLabelDisp === void 0 ? 'off' : _props$valueLabelDisp,
    _props$valueLabelForm = props.valueLabelFormat,
    valueLabelFormat = _props$valueLabelForm === void 0 ? Identity : _props$valueLabelForm,
    other = _objectWithoutProperties(props, ["aria-label", "aria-valuetext", "aria-labelledby", "component", "components", "componentsProps", "color", "classes", "className", "disableSwap", "disabled", "getAriaLabel", "getAriaValueText", "marks", "max", "min", "name", "onChange", "onChangeCommitted", "orientation", "shiftStep", "size", "step", "scale", "slotProps", "slots", "tabIndex", "track", "value", "valueLabelDisplay", "valueLabelFormat"]);
  var ownerState = _extends({}, props, {
    isRtl: isRtl,
    max: max,
    min: min,
    classes: classesProp,
    disabled: disabled,
    disableSwap: disableSwap,
    orientation: orientation,
    marks: marksProp,
    color: color,
    size: size,
    step: step,
    shiftStep: shiftStep,
    scale: scale,
    track: track,
    valueLabelDisplay: valueLabelDisplay,
    valueLabelFormat: valueLabelFormat
  });
  var _useSlider = useSlider(_extends({}, ownerState, {
      rootRef: ref
    })),
    axisProps = _useSlider.axisProps,
    getRootProps = _useSlider.getRootProps,
    getHiddenInputProps = _useSlider.getHiddenInputProps,
    getThumbProps = _useSlider.getThumbProps,
    open = _useSlider.open,
    active = _useSlider.active,
    axis = _useSlider.axis,
    focusedThumbIndex = _useSlider.focusedThumbIndex,
    range = _useSlider.range,
    dragging = _useSlider.dragging,
    marks = _useSlider.marks,
    values = _useSlider.values,
    trackOffset = _useSlider.trackOffset,
    trackLeap = _useSlider.trackLeap,
    getThumbStyle = _useSlider.getThumbStyle;
  ownerState.marked = marks.length > 0 && marks.some(function (mark) {
    return mark.label;
  });
  ownerState.dragging = dragging;
  ownerState.focusedThumbIndex = focusedThumbIndex;
  var classes = useUtilityClasses(ownerState);

  // support both `slots` and `components` for backward compatibility
  var RootSlot = (_ref10 = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : components.Root) != null ? _ref10 : SliderRoot;
  var RailSlot = (_ref11 = (_slots$rail = slots == null ? void 0 : slots.rail) != null ? _slots$rail : components.Rail) != null ? _ref11 : SliderRail;
  var TrackSlot = (_ref12 = (_slots$track = slots == null ? void 0 : slots.track) != null ? _slots$track : components.Track) != null ? _ref12 : SliderTrack;
  var ThumbSlot = (_ref13 = (_slots$thumb = slots == null ? void 0 : slots.thumb) != null ? _slots$thumb : components.Thumb) != null ? _ref13 : SliderThumb;
  var ValueLabelSlot = (_ref14 = (_slots$valueLabel = slots == null ? void 0 : slots.valueLabel) != null ? _slots$valueLabel : components.ValueLabel) != null ? _ref14 : SliderValueLabel;
  var MarkSlot = (_ref15 = (_slots$mark = slots == null ? void 0 : slots.mark) != null ? _slots$mark : components.Mark) != null ? _ref15 : SliderMark;
  var MarkLabelSlot = (_ref16 = (_slots$markLabel = slots == null ? void 0 : slots.markLabel) != null ? _slots$markLabel : components.MarkLabel) != null ? _ref16 : SliderMarkLabel;
  var InputSlot = (_ref17 = (_slots$input = slots == null ? void 0 : slots.input) != null ? _slots$input : components.Input) != null ? _ref17 : 'input';
  var rootSlotProps = (_slotProps$root = slotProps == null ? void 0 : slotProps.root) != null ? _slotProps$root : componentsProps.root;
  var railSlotProps = (_slotProps$rail = slotProps == null ? void 0 : slotProps.rail) != null ? _slotProps$rail : componentsProps.rail;
  var trackSlotProps = (_slotProps$track = slotProps == null ? void 0 : slotProps.track) != null ? _slotProps$track : componentsProps.track;
  var thumbSlotProps = (_slotProps$thumb = slotProps == null ? void 0 : slotProps.thumb) != null ? _slotProps$thumb : componentsProps.thumb;
  var valueLabelSlotProps = (_slotProps$valueLabel = slotProps == null ? void 0 : slotProps.valueLabel) != null ? _slotProps$valueLabel : componentsProps.valueLabel;
  var markSlotProps = (_slotProps$mark = slotProps == null ? void 0 : slotProps.mark) != null ? _slotProps$mark : componentsProps.mark;
  var markLabelSlotProps = (_slotProps$markLabel = slotProps == null ? void 0 : slotProps.markLabel) != null ? _slotProps$markLabel : componentsProps.markLabel;
  var inputSlotProps = (_slotProps$input = slotProps == null ? void 0 : slotProps.input) != null ? _slotProps$input : componentsProps.input;
  var rootProps = useSlotProps({
    elementType: RootSlot,
    getSlotProps: getRootProps,
    externalSlotProps: rootSlotProps,
    externalForwardedProps: other,
    additionalProps: _extends({}, shouldSpreadAdditionalProps(RootSlot) && {
      as: component
    }),
    ownerState: _extends({}, ownerState, rootSlotProps == null ? void 0 : rootSlotProps.ownerState),
    className: [classes.root, className]
  });
  var railProps = useSlotProps({
    elementType: RailSlot,
    externalSlotProps: railSlotProps,
    ownerState: ownerState,
    className: classes.rail
  });
  var trackProps = useSlotProps({
    elementType: TrackSlot,
    externalSlotProps: trackSlotProps,
    additionalProps: {
      style: _extends({}, axisProps[axis].offset(trackOffset), axisProps[axis].leap(trackLeap))
    },
    ownerState: _extends({}, ownerState, trackSlotProps == null ? void 0 : trackSlotProps.ownerState),
    className: classes.track
  });
  var thumbProps = useSlotProps({
    elementType: ThumbSlot,
    getSlotProps: getThumbProps,
    externalSlotProps: thumbSlotProps,
    ownerState: _extends({}, ownerState, thumbSlotProps == null ? void 0 : thumbSlotProps.ownerState),
    className: classes.thumb
  });
  var valueLabelProps = useSlotProps({
    elementType: ValueLabelSlot,
    externalSlotProps: valueLabelSlotProps,
    ownerState: _extends({}, ownerState, valueLabelSlotProps == null ? void 0 : valueLabelSlotProps.ownerState),
    className: classes.valueLabel
  });
  var markProps = useSlotProps({
    elementType: MarkSlot,
    externalSlotProps: markSlotProps,
    ownerState: ownerState,
    className: classes.mark
  });
  var markLabelProps = useSlotProps({
    elementType: MarkLabelSlot,
    externalSlotProps: markLabelSlotProps,
    ownerState: ownerState,
    className: classes.markLabel
  });
  var inputSliderProps = useSlotProps({
    elementType: InputSlot,
    getSlotProps: getHiddenInputProps,
    externalSlotProps: inputSlotProps,
    ownerState: ownerState
  });
  return /*#__PURE__*/_jsxs(RootSlot, _extends({}, rootProps, {
    children: [/*#__PURE__*/_jsx(RailSlot, _extends({}, railProps)), /*#__PURE__*/_jsx(TrackSlot, _extends({}, trackProps)), marks.filter(function (mark) {
      return mark.value >= min && mark.value <= max;
    }).map(function (mark, index) {
      var percent = valueToPercent(mark.value, min, max);
      var style = axisProps[axis].offset(percent);
      var markActive;
      if (track === false) {
        markActive = values.indexOf(mark.value) !== -1;
      } else {
        markActive = track === 'normal' && (range ? mark.value >= values[0] && mark.value <= values[values.length - 1] : mark.value <= values[0]) || track === 'inverted' && (range ? mark.value <= values[0] || mark.value >= values[values.length - 1] : mark.value >= values[0]);
      }
      return /*#__PURE__*/_jsxs(React.Fragment, {
        children: [/*#__PURE__*/_jsx(MarkSlot, _extends({
          "data-index": index
        }, markProps, !isHostComponent(MarkSlot) && {
          markActive: markActive
        }, {
          style: _extends({}, style, markProps.style),
          className: clsx(markProps.className, markActive && classes.markActive)
        })), mark.label != null ? /*#__PURE__*/_jsx(MarkLabelSlot, _extends({
          "aria-hidden": true,
          "data-index": index
        }, markLabelProps, !isHostComponent(MarkLabelSlot) && {
          markLabelActive: markActive
        }, {
          style: _extends({}, style, markLabelProps.style),
          className: clsx(classes.markLabel, markLabelProps.className, markActive && classes.markLabelActive),
          children: mark.label
        })) : null]
      }, index);
    }), values.map(function (value, index) {
      var percent = valueToPercent(value, min, max);
      var style = axisProps[axis].offset(percent);
      var ValueLabelComponent = valueLabelDisplay === 'off' ? Forward : ValueLabelSlot;
      return (
        /*#__PURE__*/
        /* TODO v6: Change component structure. It will help in avoiding the complicated React.cloneElement API added in SliderValueLabel component. Should be: Thumb -> Input, ValueLabel. Follow Joy UI's Slider structure. */
        _jsx(ValueLabelComponent, _extends({}, !isHostComponent(ValueLabelComponent) && {
          valueLabelFormat: valueLabelFormat,
          valueLabelDisplay: valueLabelDisplay,
          value: typeof valueLabelFormat === 'function' ? valueLabelFormat(scale(value), index) : valueLabelFormat,
          index: index,
          open: open === index || active === index || valueLabelDisplay === 'on',
          disabled: disabled
        }, valueLabelProps, {
          children: /*#__PURE__*/_jsx(ThumbSlot, _extends({
            "data-index": index
          }, thumbProps, {
            className: clsx(classes.thumb, thumbProps.className, active === index && classes.active, focusedThumbIndex === index && classes.focusVisible),
            style: _extends({}, style, getThumbStyle(index), thumbProps.style),
            children: /*#__PURE__*/_jsx(InputSlot, _extends({
              "data-index": index,
              "aria-label": getAriaLabel ? getAriaLabel(index) : ariaLabel,
              "aria-valuenow": scale(value),
              "aria-labelledby": ariaLabelledby,
              "aria-valuetext": getAriaValueText ? getAriaValueText(scale(value), index) : ariaValuetext,
              value: values[index]
            }, inputSliderProps))
          }))
        }), index)
      );
    })]
  }));
});
process.env.NODE_ENV !== "production" ? Slider.propTypes /* remove-proptypes */ = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The label of the slider.
   */
  'aria-label': chainPropTypes(PropTypes.string, function (props) {
    var range = Array.isArray(props.value || props.defaultValue);
    if (range && props['aria-label'] != null) {
      return new Error('MUI: You need to use the `getAriaLabel` prop instead of `aria-label` when using a range slider.');
    }
    return null;
  }),
  /**
   * The id of the element containing a label for the slider.
   */
  'aria-labelledby': PropTypes.string,
  /**
   * A string value that provides a user-friendly name for the current value of the slider.
   */
  'aria-valuetext': chainPropTypes(PropTypes.string, function (props) {
    var range = Array.isArray(props.value || props.defaultValue);
    if (range && props['aria-valuetext'] != null) {
      return new Error('MUI: You need to use the `getAriaValueText` prop instead of `aria-valuetext` when using a range slider.');
    }
    return null;
  }),
  /**
   * @ignore
   */
  children: PropTypes.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: PropTypes.object,
  /**
   * @ignore
   */
  className: PropTypes.string,
  /**
   * The color of the component.
   * It supports both default and custom theme colors, which can be added as shown in the
   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).
   * @default 'primary'
   */
  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),
  /**
   * The components used for each slot inside.
   *
   * @deprecated use the `slots` prop instead. This prop will be removed in v7. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/).
   *
   * @default {}
   */
  components: PropTypes.shape({
    Input: PropTypes.elementType,
    Mark: PropTypes.elementType,
    MarkLabel: PropTypes.elementType,
    Rail: PropTypes.elementType,
    Root: PropTypes.elementType,
    Thumb: PropTypes.elementType,
    Track: PropTypes.elementType,
    ValueLabel: PropTypes.elementType
  }),
  /**
   * The extra props for the slot components.
   * You can override the existing props or add new ones.
   *
   * @deprecated use the `slotProps` prop instead. This prop will be removed in v7. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/).
   *
   * @default {}
   */
  componentsProps: PropTypes.shape({
    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    mark: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    markLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    rail: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    valueLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({
      children: PropTypes.element,
      className: PropTypes.string,
      open: PropTypes.bool,
      style: PropTypes.object,
      value: PropTypes.number,
      valueLabelDisplay: PropTypes.oneOf(['auto', 'off', 'on'])
    })])
  }),
  /**
   * The default value. Use when the component is not controlled.
   */
  defaultValue: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.number), PropTypes.number]),
  /**
   * If `true`, the component is disabled.
   * @default false
   */
  disabled: PropTypes.bool,
  /**
   * If `true`, the active thumb doesn't swap when moving pointer over a thumb while dragging another thumb.
   * @default false
   */
  disableSwap: PropTypes.bool,
  /**
   * Accepts a function which returns a string value that provides a user-friendly name for the thumb labels of the slider.
   * This is important for screen reader users.
   * @param {number} index The thumb label's index to format.
   * @returns {string}
   */
  getAriaLabel: PropTypes.func,
  /**
   * Accepts a function which returns a string value that provides a user-friendly name for the current value of the slider.
   * This is important for screen reader users.
   * @param {number} value The thumb label's value to format.
   * @param {number} index The thumb label's index to format.
   * @returns {string}
   */
  getAriaValueText: PropTypes.func,
  /**
   * Marks indicate predetermined values to which the user can move the slider.
   * If `true` the marks are spaced according the value of the `step` prop.
   * If an array, it should contain objects with `value` and an optional `label` keys.
   * @default false
   */
  marks: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.node,
    value: PropTypes.number.isRequired
  })), PropTypes.bool]),
  /**
   * The maximum allowed value of the slider.
   * Should not be equal to min.
   * @default 100
   */
  max: PropTypes.number,
  /**
   * The minimum allowed value of the slider.
   * Should not be equal to max.
   * @default 0
   */
  min: PropTypes.number,
  /**
   * Name attribute of the hidden `input` element.
   */
  name: PropTypes.string,
  /**
   * Callback function that is fired when the slider's value changed.
   *
   * @param {Event} event The event source of the callback.
   * You can pull out the new value by accessing `event.target.value` (any).
   * **Warning**: This is a generic event not a change event.
   * @param {number | number[]} value The new value.
   * @param {number} activeThumb Index of the currently moved thumb.
   */
  onChange: PropTypes.func,
  /**
   * Callback function that is fired when the `mouseup` is triggered.
   *
   * @param {React.SyntheticEvent | Event} event The event source of the callback. **Warning**: This is a generic event not a change event.
   * @param {number | number[]} value The new value.
   */
  onChangeCommitted: PropTypes.func,
  /**
   * The component orientation.
   * @default 'horizontal'
   */
  orientation: PropTypes.oneOf(['horizontal', 'vertical']),
  /**
   * A transformation function, to change the scale of the slider.
   * @param {any} x
   * @returns {any}
   * @default function Identity(x) {
   *   return x;
   * }
   */
  scale: PropTypes.func,
  /**
   * The granularity with which the slider can step through values when using Page Up/Page Down or Shift + Arrow Up/Arrow Down.
   * @default 10
   */
  shiftStep: PropTypes.number,
  /**
   * The size of the slider.
   * @default 'medium'
   */
  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium']), PropTypes.string]),
  /**
   * The props used for each slot inside the Slider.
   * @default {}
   */
  slotProps: PropTypes.shape({
    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    mark: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    markLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    rail: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    valueLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({
      children: PropTypes.element,
      className: PropTypes.string,
      open: PropTypes.bool,
      style: PropTypes.object,
      value: PropTypes.number,
      valueLabelDisplay: PropTypes.oneOf(['auto', 'off', 'on'])
    })])
  }),
  /**
   * The components used for each slot inside the Slider.
   * Either a string to use a HTML element or a component.
   * @default {}
   */
  slots: PropTypes.shape({
    input: PropTypes.elementType,
    mark: PropTypes.elementType,
    markLabel: PropTypes.elementType,
    rail: PropTypes.elementType,
    root: PropTypes.elementType,
    thumb: PropTypes.elementType,
    track: PropTypes.elementType,
    valueLabel: PropTypes.elementType
  }),
  /**
   * The granularity with which the slider can step through values. (A "discrete" slider.)
   * The `min` prop serves as the origin for the valid values.
   * We recommend (max - min) to be evenly divisible by the step.
   *
   * When step is `null`, the thumb can only be slid onto marks provided with the `marks` prop.
   * @default 1
   */
  step: PropTypes.number,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),
  /**
   * Tab index attribute of the hidden `input` element.
   */
  tabIndex: PropTypes.number,
  /**
   * The track presentation:
   *
   * - `normal` the track will render a bar representing the slider value.
   * - `inverted` the track will render a bar representing the remaining slider value.
   * - `false` the track will render without a bar.
   * @default 'normal'
   */
  track: PropTypes.oneOf(['inverted', 'normal', false]),
  /**
   * The value of the slider.
   * For ranged sliders, provide an array with two values.
   */
  value: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.number), PropTypes.number]),
  /**
   * Controls when the value label is displayed:
   *
   * - `auto` the value label will display when the thumb is hovered or focused.
   * - `on` will display persistently.
   * - `off` will never display.
   * @default 'off'
   */
  valueLabelDisplay: PropTypes.oneOf(['auto', 'off', 'on']),
  /**
   * The format function the value label's value.
   *
   * When a function is provided, it should have the following signature:
   *
   * - {number} value The value label's value to format
   * - {number} index The value label's index to format
   * @param {any} x
   * @returns {any}
   * @default function Identity(x) {
   *   return x;
   * }
   */
  valueLabelFormat: PropTypes.oneOfType([PropTypes.func, PropTypes.string])
} : void 0;
export default Slider;