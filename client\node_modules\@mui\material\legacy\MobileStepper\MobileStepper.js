'use client';

import _toConsumableArray from "@babel/runtime/helpers/esm/toConsumableArray";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _extends from "@babel/runtime/helpers/esm/extends";
import * as React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import integerPropType from '@mui/utils/integerPropType';
import composeClasses from '@mui/utils/composeClasses';
import Paper from '../Paper';
import capitalize from '../utils/capitalize';
import LinearProgress from '../LinearProgress';
import { useDefaultProps } from '../DefaultPropsProvider';
import styled, { slotShouldForwardProp } from '../styles/styled';
import { getMobileStepperUtilityClass } from './mobileStepperClasses';
import { jsxs as _jsxs } from "react/jsx-runtime";
import { jsx as _jsx } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes,
    position = ownerState.position;
  var slots = {
    root: ['root', "position".concat(capitalize(position))],
    dots: ['dots'],
    dot: ['dot'],
    dotActive: ['dotActive'],
    progress: ['progress']
  };
  return composeClasses(slots, getMobileStepperUtilityClass, classes);
};
var MobileStepperRoot = styled(Paper, {
  name: 'MuiMobileStepper',
  slot: 'Root',
  overridesResolver: function overridesResolver(props, styles) {
    var ownerState = props.ownerState;
    return [styles.root, styles["position".concat(capitalize(ownerState.position))]];
  }
})(function (_ref) {
  var theme = _ref.theme,
    ownerState = _ref.ownerState;
  return _extends({
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    background: (theme.vars || theme).palette.background.default,
    padding: 8
  }, ownerState.position === 'bottom' && {
    position: 'fixed',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: (theme.vars || theme).zIndex.mobileStepper
  }, ownerState.position === 'top' && {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    zIndex: (theme.vars || theme).zIndex.mobileStepper
  });
});
var MobileStepperDots = styled('div', {
  name: 'MuiMobileStepper',
  slot: 'Dots',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.dots;
  }
})(function (_ref2) {
  var ownerState = _ref2.ownerState;
  return _extends({}, ownerState.variant === 'dots' && {
    display: 'flex',
    flexDirection: 'row'
  });
});
var MobileStepperDot = styled('div', {
  name: 'MuiMobileStepper',
  slot: 'Dot',
  shouldForwardProp: function shouldForwardProp(prop) {
    return slotShouldForwardProp(prop) && prop !== 'dotActive';
  },
  overridesResolver: function overridesResolver(props, styles) {
    var dotActive = props.dotActive;
    return [styles.dot, dotActive && styles.dotActive];
  }
})(function (_ref3) {
  var theme = _ref3.theme,
    ownerState = _ref3.ownerState,
    dotActive = _ref3.dotActive;
  return _extends({}, ownerState.variant === 'dots' && _extends({
    transition: theme.transitions.create('background-color', {
      duration: theme.transitions.duration.shortest
    }),
    backgroundColor: (theme.vars || theme).palette.action.disabled,
    borderRadius: '50%',
    width: 8,
    height: 8,
    margin: '0 2px'
  }, dotActive && {
    backgroundColor: (theme.vars || theme).palette.primary.main
  }));
});
var MobileStepperProgress = styled(LinearProgress, {
  name: 'MuiMobileStepper',
  slot: 'Progress',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.progress;
  }
})(function (_ref4) {
  var ownerState = _ref4.ownerState;
  return _extends({}, ownerState.variant === 'progress' && {
    width: '50%'
  });
});
var MobileStepper = /*#__PURE__*/React.forwardRef(function MobileStepper(inProps, ref) {
  var props = useDefaultProps({
    props: inProps,
    name: 'MuiMobileStepper'
  });
  var _props$activeStep = props.activeStep,
    activeStep = _props$activeStep === void 0 ? 0 : _props$activeStep,
    backButton = props.backButton,
    className = props.className,
    LinearProgressProps = props.LinearProgressProps,
    nextButton = props.nextButton,
    _props$position = props.position,
    position = _props$position === void 0 ? 'bottom' : _props$position,
    steps = props.steps,
    _props$variant = props.variant,
    variant = _props$variant === void 0 ? 'dots' : _props$variant,
    other = _objectWithoutProperties(props, ["activeStep", "backButton", "className", "LinearProgressProps", "nextButton", "position", "steps", "variant"]);
  var ownerState = _extends({}, props, {
    activeStep: activeStep,
    position: position,
    variant: variant
  });
  var value;
  if (variant === 'progress') {
    if (steps === 1) {
      value = 100;
    } else {
      value = Math.ceil(activeStep / (steps - 1) * 100);
    }
  }
  var classes = useUtilityClasses(ownerState);
  return /*#__PURE__*/_jsxs(MobileStepperRoot, _extends({
    square: true,
    elevation: 0,
    className: clsx(classes.root, className),
    ref: ref,
    ownerState: ownerState
  }, other, {
    children: [backButton, variant === 'text' && /*#__PURE__*/_jsxs(React.Fragment, {
      children: [activeStep + 1, " / ", steps]
    }), variant === 'dots' && /*#__PURE__*/_jsx(MobileStepperDots, {
      ownerState: ownerState,
      className: classes.dots,
      children: _toConsumableArray(new Array(steps)).map(function (_, index) {
        return /*#__PURE__*/_jsx(MobileStepperDot, {
          className: clsx(classes.dot, index === activeStep && classes.dotActive),
          ownerState: ownerState,
          dotActive: index === activeStep
        }, index);
      })
    }), variant === 'progress' && /*#__PURE__*/_jsx(MobileStepperProgress, _extends({
      ownerState: ownerState,
      className: classes.progress,
      variant: "determinate",
      value: value
    }, LinearProgressProps)), nextButton]
  }));
});
process.env.NODE_ENV !== "production" ? MobileStepper.propTypes /* remove-proptypes */ = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * Set the active step (zero based index).
   * Defines which dot is highlighted when the variant is 'dots'.
   * @default 0
   */
  activeStep: integerPropType,
  /**
   * A back button element. For instance, it can be a `Button` or an `IconButton`.
   */
  backButton: PropTypes.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: PropTypes.object,
  /**
   * @ignore
   */
  className: PropTypes.string,
  /**
   * Props applied to the `LinearProgress` element.
   */
  LinearProgressProps: PropTypes.object,
  /**
   * A next button element. For instance, it can be a `Button` or an `IconButton`.
   */
  nextButton: PropTypes.node,
  /**
   * Set the positioning type.
   * @default 'bottom'
   */
  position: PropTypes.oneOf(['bottom', 'static', 'top']),
  /**
   * The total steps.
   */
  steps: integerPropType.isRequired,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),
  /**
   * The variant to use.
   * @default 'dots'
   */
  variant: PropTypes.oneOf(['dots', 'progress', 'text'])
} : void 0;
export default MobileStepper;